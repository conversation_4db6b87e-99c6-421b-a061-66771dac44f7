import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { BullModule } from '@nestjs/bull';
import { ExecutionController } from './execution.controller';
import { ExecutionService } from './execution.service';
import { EnhancedExecutionService } from './enhanced-execution.service';
import { ScriptExecution } from '../visual-script/entities/script-execution.entity';
import { VisualScript } from '../visual-script/entities/visual-script.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([ScriptExecution, VisualScript]),
    BullModule.registerQueue({
      name: 'script-execution',
    }),
  ],
  controllers: [ExecutionController],
  providers: [ExecutionService, EnhancedExecutionService],
  exports: [ExecutionService, EnhancedExecutionService],
})
export class ExecutionModule {}
