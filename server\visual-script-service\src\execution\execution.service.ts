import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { InjectQueue } from '@nestjs/bull';
import { Repository } from 'typeorm';
import { Queue } from 'bull';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ScriptExecution, ExecutionStatus } from '../visual-script/entities/script-execution.entity';
import { VisualScript } from '../visual-script/entities/visual-script.entity';
import { ExecuteScriptDto, QueryExecutionDto } from './dto/execution.dto';
import { EnhancedExecutionService } from './enhanced-execution.service';

@Injectable()
export class ExecutionService {
  constructor(
    @InjectRepository(ScriptExecution)
    private executionRepository: Repository<ScriptExecution>,
    @InjectRepository(VisualScript)
    private scriptRepository: Repository<VisualScript>,
    @InjectQueue('script-execution')
    private executionQueue: Queue,
    private eventEmitter: EventEmitter2,
    private enhancedExecutionService: EnhancedExecutionService,
  ) {}

  /**
   * 执行脚本
   */
  async executeScript(scriptId: string, executeDto: ExecuteScriptDto, userId: string) {
    // 获取脚本
    const script = await this.scriptRepository.findOne({
      where: { id: scriptId },
      relations: ['collaborators'],
    });

    if (!script) {
      throw new NotFoundException('脚本不存在');
    }

    // 检查执行权限
    if (!this.canExecuteScript(script, userId)) {
      throw new ForbiddenException('没有执行权限');
    }

    // 创建执行记录
    const execution = this.executionRepository.create({
      scriptId,
      executedBy: userId,
      executedByName: 'User', // 可以从用户服务获取真实姓名
      status: ExecutionStatus.PENDING,
      inputParameters: executeDto.input || {},
      executionContext: executeDto.config || {},
      startedAt: new Date(),
    });

    const savedExecution = await this.executionRepository.save(execution);

    // 添加到执行队列
    await this.executionQueue.add('execute-script', {
      executionId: savedExecution.id,
      scriptId,
      userId,
      input: executeDto.input,
      config: executeDto.config,
    }, {
      attempts: 3,
      backoff: {
        type: 'exponential',
        delay: 2000,
      },
    });

    // 发送执行开始事件
    this.eventEmitter.emit('script.execution.started', {
      executionId: savedExecution.id,
      scriptId,
      userId,
    });

    return savedExecution;
  }

  /**
   * 获取执行列表
   */
  async getExecutions(queryDto: QueryExecutionDto, userId: string) {
    const { page = 1, limit = 20, status, scriptId, sortBy = 'startTime', sortOrder = 'DESC' } = queryDto;
    const skip = (page - 1) * limit;

    const queryBuilder = this.executionRepository.createQueryBuilder('execution')
      .leftJoinAndSelect('execution.script', 'script')
      .where('execution.userId = :userId', { userId });

    if (status) {
      queryBuilder.andWhere('execution.status = :status', { status });
    }

    if (scriptId) {
      queryBuilder.andWhere('execution.scriptId = :scriptId', { scriptId });
    }

    queryBuilder
      .orderBy(`execution.${sortBy}`, sortOrder as 'ASC' | 'DESC')
      .skip(skip)
      .take(limit);

    const [executions, total] = await queryBuilder.getManyAndCount();

    return {
      executions,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * 获取执行详情
   */
  async getExecution(executionId: string, userId: string) {
    const execution = await this.executionRepository.findOne({
      where: { id: executionId },
      relations: ['script'],
    });

    if (!execution) {
      throw new NotFoundException('执行记录不存在');
    }

    if (!this.canAccessExecution(execution, userId)) {
      throw new ForbiddenException('没有访问权限');
    }

    return execution;
  }

  /**
   * 停止脚本执行
   */
  async stopExecution(executionId: string, userId: string) {
    const execution = await this.getExecution(executionId, userId);

    if (execution.status !== ExecutionStatus.RUNNING) {
      throw new BadRequestException('只能停止正在运行的脚本');
    }

    // 更新状态
    execution.cancel();

    await this.executionRepository.save(execution);

    // 从队列中移除任务
    const job = await this.executionQueue.getJob(executionId);
    if (job) {
      await job.remove();
    }

    // 发送停止事件
    this.eventEmitter.emit('script.execution.stopped', {
      executionId,
      userId,
    });

    return execution;
  }

  /**
   * 删除执行记录
   */
  async deleteExecution(executionId: string, userId: string) {
    const execution = await this.getExecution(executionId, userId);

    if (execution.status === 'running') {
      throw new BadRequestException('不能删除正在运行的执行记录');
    }

    await this.executionRepository.remove(execution);

    // 发送删除事件
    this.eventEmitter.emit('script.execution.deleted', {
      executionId,
      userId,
    });
  }

  /**
   * 获取脚本的执行历史
   */
  async getScriptExecutions(scriptId: string, queryDto: QueryExecutionDto, userId: string) {
    const script = await this.scriptRepository.findOne({
      where: { id: scriptId },
      relations: ['collaborators'],
    });

    if (!script) {
      throw new NotFoundException('脚本不存在');
    }

    if (!this.canAccessScript(script, userId)) {
      throw new ForbiddenException('没有访问权限');
    }

    return this.getExecutions({ ...queryDto, scriptId }, userId);
  }

  /**
   * 获取执行日志
   */
  async getExecutionLogs(executionId: string, userId: string) {
    const execution = await this.getExecution(executionId, userId);
    
    return {
      executionId,
      logs: [], // 日志需要从其他地方获取
      output: execution.outputResults,
      error: execution.errorMessage,
    };
  }

  /**
   * 获取执行统计
   */
  async getExecutionStats(userId: string) {
    const stats = await this.executionRepository
      .createQueryBuilder('execution')
      .select([
        'COUNT(*) as total',
        'SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as completed',
        'SUM(CASE WHEN status = "failed" THEN 1 ELSE 0 END) as failed',
        'SUM(CASE WHEN status = "running" THEN 1 ELSE 0 END) as running',
        'AVG(duration) as avgDuration',
      ])
      .where('userId = :userId', { userId })
      .getRawOne();

    return {
      total: parseInt(stats.total) || 0,
      completed: parseInt(stats.completed) || 0,
      failed: parseInt(stats.failed) || 0,
      running: parseInt(stats.running) || 0,
      avgDuration: parseFloat(stats.avgDuration) || 0,
    };
  }

  /**
   * 检查脚本执行权限
   */
  private canExecuteScript(script: VisualScript, userId: string): boolean {
    if (script.ownerId === userId) {
      return true;
    }

    // 检查协作者权限
    const collaborator = script.collaborators?.find(c => c.userId === userId);
    return collaborator && ['owner', 'editor', 'executor'].includes(collaborator.role);
  }

  /**
   * 检查脚本访问权限
   */
  private canAccessScript(script: VisualScript, userId: string): boolean {
    if (script.ownerId === userId) {
      return true;
    }

    if (script.visibility === 'public') {
      return true;
    }

    // 检查协作者权限
    const collaborator = script.collaborators?.find(c => c.userId === userId);
    return !!collaborator;
  }

  /**
   * 检查执行记录访问权限
   */
  private canAccessExecution(execution: ScriptExecution, userId: string): boolean {
    if (execution.executedBy === userId) {
      return true;
    }

    // 检查脚本权限
    return this.canAccessScript(execution.script, userId);
  }
}
