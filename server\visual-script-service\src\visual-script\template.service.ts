import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ScriptTemplate } from './entities/script-template.entity';
import { VisualScript } from './entities/visual-script.entity';
import { CreateTemplateDto, UpdateTemplateDto, QueryTemplateDto } from './dto/template.dto';

@Injectable()
export class TemplateService {
  constructor(
    @InjectRepository(ScriptTemplate)
    private templateRepository: Repository<ScriptTemplate>,
    @InjectRepository(VisualScript)
    private scriptRepository: Repository<VisualScript>,
    private eventEmitter: EventEmitter2,
  ) {}

  /**
   * 创建脚本模板
   */
  async createTemplate(createTemplateDto: CreateTemplateDto, userId: string) {
    // 检查模板名称是否已存在
    const existingTemplate = await this.templateRepository.findOne({
      where: { name: createTemplateDto.name, createdBy: userId },
    });

    if (existingTemplate) {
      throw new BadRequestException('模板名称已存在');
    }

    // 创建模板
    const template = this.templateRepository.create({
      ...createTemplateDto,
      createdBy: userId,
      createdByName: 'User', // 可以从用户服务获取真实姓名
    });

    const savedTemplate = await this.templateRepository.save(template);

    // 发送模板创建事件
    this.eventEmitter.emit('template.created', {
      templateId: savedTemplate.id,
      userId,
    });

    return savedTemplate;
  }

  /**
   * 获取模板列表
   */
  async getTemplates(queryDto: QueryTemplateDto, userId: string) {
    const { 
      page = 1, 
      limit = 20, 
      category, 
      tags, 
      search, 
      isPublic,
      sortBy = 'createdAt', 
      sortOrder = 'DESC' 
    } = queryDto;
    const skip = (page - 1) * limit;

    const queryBuilder = this.templateRepository.createQueryBuilder('template');

    // 基础过滤条件
    if (isPublic !== undefined) {
      queryBuilder.andWhere('template.isPublic = :isPublic', { isPublic });
    } else {
      // 默认显示公开模板和用户自己的模板
      queryBuilder.andWhere(
        '(template.isPublic = true OR template.createdBy = :userId)',
        { userId }
      );
    }

    // 分类过滤
    if (category) {
      queryBuilder.andWhere('template.category = :category', { category });
    }

    // 标签过滤
    if (tags) {
      const tagArray = tags.split(',');
      queryBuilder.andWhere('template.tags && :tags', { tags: tagArray });
    }

    // 搜索过滤
    if (search) {
      queryBuilder.andWhere(
        '(template.name LIKE :search OR template.description LIKE :search)',
        { search: `%${search}%` }
      );
    }

    // 排序
    queryBuilder.orderBy(`template.${sortBy}`, sortOrder as 'ASC' | 'DESC');

    // 分页
    queryBuilder.skip(skip).take(limit);

    const [templates, total] = await queryBuilder.getManyAndCount();

    return {
      templates,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * 获取模板分类
   */
  async getTemplateCategories() {
    const categories = await this.templateRepository
      .createQueryBuilder('template')
      .select('template.category', 'category')
      .addSelect('COUNT(*)', 'count')
      .where('template.isPublic = true')
      .groupBy('template.category')
      .getRawMany();

    return categories.map(cat => ({
      name: cat.category,
      count: parseInt(cat.count),
    }));
  }

  /**
   * 获取热门模板
   */
  async getPopularTemplates(queryDto: QueryTemplateDto) {
    const { limit = 10 } = queryDto;

    const templates = await this.templateRepository
      .createQueryBuilder('template')
      .where('template.isPublic = true')
      .orderBy('template.usageCount', 'DESC')
      .addOrderBy('template.likeCount', 'DESC')
      .take(limit)
      .getMany();

    return { templates };
  }

  /**
   * 获取模板详情
   */
  async getTemplate(templateId: string, userId: string) {
    const template = await this.templateRepository.findOne({
      where: { id: templateId },
    });

    if (!template) {
      throw new NotFoundException('模板不存在');
    }

    // 检查访问权限
    if (!this.canAccessTemplate(template, userId)) {
      throw new ForbiddenException('没有访问权限');
    }

    // 增加浏览次数
    template.viewCount = (template.viewCount || 0) + 1;
    await this.templateRepository.save(template);

    return template;
  }

  /**
   * 更新模板
   */
  async updateTemplate(templateId: string, updateTemplateDto: UpdateTemplateDto, userId: string) {
    const template = await this.templateRepository.findOne({
      where: { id: templateId },
    });

    if (!template) {
      throw new NotFoundException('模板不存在');
    }

    // 检查编辑权限
    if (!this.canEditTemplate(template, userId)) {
      throw new ForbiddenException('没有编辑权限');
    }

    // 更新模板
    Object.assign(template, updateTemplateDto);
    // updatedAt 会自动更新

    const updatedTemplate = await this.templateRepository.save(template);

    // 发送模板更新事件
    this.eventEmitter.emit('template.updated', {
      templateId,
      userId,
    });

    return updatedTemplate;
  }

  /**
   * 删除模板
   */
  async deleteTemplate(templateId: string, userId: string) {
    const template = await this.templateRepository.findOne({
      where: { id: templateId },
    });

    if (!template) {
      throw new NotFoundException('模板不存在');
    }

    // 检查删除权限
    if (!this.canEditTemplate(template, userId)) {
      throw new ForbiddenException('没有删除权限');
    }

    await this.templateRepository.remove(template);

    // 发送模板删除事件
    this.eventEmitter.emit('template.deleted', {
      templateId,
      userId,
    });
  }

  /**
   * 使用模板创建脚本
   */
  async useTemplate(templateId: string, body: { name?: string; description?: string }, userId: string) {
    const template = await this.getTemplate(templateId, userId);

    // 创建新脚本
    const script = this.scriptRepository.create({
      name: body.name || `基于${template.name}的脚本`,
      description: body.description || template.description,
      graph: template.graph,
      ownerId: userId,
      ownerName: 'User', // 可以从用户服务获取真实姓名
    });

    const savedScript = await this.scriptRepository.save(script);

    // 增加模板使用次数
    template.usageCount = (template.usageCount || 0) + 1;
    await this.templateRepository.save(template);

    // 发送模板使用事件
    this.eventEmitter.emit('template.used', {
      templateId,
      scriptId: savedScript.id,
      userId,
    });

    return savedScript;
  }

  /**
   * 克隆模板
   */
  async cloneTemplate(templateId: string, body: { name?: string; description?: string }, userId: string) {
    const template = await this.getTemplate(templateId, userId);

    // 创建新模板
    const clonedTemplate = this.templateRepository.create({
      name: body.name || `${template.name} (副本)`,
      description: body.description || template.description,
      category: template.category,
      tags: template.tags,
      content: template.content,
      metadata: template.metadata,
      isPublic: false, // 克隆的模板默认为私有
      createdBy: userId,
      updatedBy: userId,
    });

    const savedTemplate = await this.templateRepository.save(clonedTemplate);

    // 发送模板克隆事件
    this.eventEmitter.emit('template.cloned', {
      originalTemplateId: templateId,
      newTemplateId: savedTemplate.id,
      userId,
    });

    return savedTemplate;
  }

  /**
   * 点赞模板
   */
  async likeTemplate(templateId: string, userId: string) {
    const template = await this.getTemplate(templateId, userId);

    // 这里应该检查用户是否已经点赞过
    // 简化实现，直接增加点赞数
    template.likeCount = (template.likeCount || 0) + 1;
    await this.templateRepository.save(template);

    // 发送点赞事件
    this.eventEmitter.emit('template.liked', {
      templateId,
      userId,
    });

    return { liked: true, likeCount: template.likeCount };
  }

  /**
   * 取消点赞模板
   */
  async unlikeTemplate(templateId: string, userId: string) {
    const template = await this.getTemplate(templateId, userId);

    // 简化实现，直接减少点赞数
    template.likeCount = Math.max((template.likeCount || 0) - 1, 0);
    await this.templateRepository.save(template);

    // 发送取消点赞事件
    this.eventEmitter.emit('template.unliked', {
      templateId,
      userId,
    });

    return { liked: false, likeCount: template.likeCount };
  }

  /**
   * 检查模板访问权限
   */
  private canAccessTemplate(template: ScriptTemplate, userId: string): boolean {
    if (template.isPublic) {
      return true;
    }

    return template.createdBy === userId;
  }

  /**
   * 检查模板编辑权限
   */
  private canEditTemplate(template: ScriptTemplate, userId: string): boolean {
    return template.createdBy === userId;
  }
}
