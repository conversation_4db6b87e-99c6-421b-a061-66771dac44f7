/**
 * 增强的脚本执行服务
 * 提供完整的脚本执行、监控和管理功能
 */
import { Injectable, Logger, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ScriptExecution, ExecutionStatus, ExecutionEnvironment } from '../visual-script/entities/script-execution.entity';
import { VisualScript } from '../visual-script/entities/visual-script.entity';

// 执行上下文接口
export interface ExecutionContext {
  userId: string;
  parameters: Record<string, any>;
  environment: string;
  timeout: number;
  debug: boolean;
  maxMemory?: number;
  maxCpuTime?: number;
}

// 执行结果接口
export interface ExecutionResult {
  id: string;
  status: ExecutionStatus;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  output?: any;
  error?: string;
  logs: string[];
  performance: {
    memoryUsage: number;
    cpuTime: number;
    nodeExecutions: number;
  };
}

// 节点执行信息接口
export interface NodeExecutionInfo {
  nodeId: string;
  nodeType: string;
  startTime: number;
  endTime: number;
  duration: number;
  status: 'success' | 'error' | 'warning';
  input?: any;
  output?: any;
  error?: string;
}

@Injectable()
export class EnhancedExecutionService {
  private readonly logger = new Logger(EnhancedExecutionService.name);
  private readonly activeExecutions = new Map<string, any>();

  constructor(
    @InjectRepository(ScriptExecution)
    private readonly executionRepository: Repository<ScriptExecution>,
    @InjectRepository(VisualScript)
    private readonly scriptRepository: Repository<VisualScript>,
    @InjectQueue('script-execution')
    private readonly executionQueue: Queue,
    private readonly eventEmitter: EventEmitter2
  ) {}

  /**
   * 执行脚本
   */
  async executeScript(scriptId: string, context: ExecutionContext): Promise<ExecutionResult> {
    this.logger.log(`开始执行脚本: ${scriptId}`);

    // 获取脚本
    const script = await this.scriptRepository.findOne({
      where: { id: scriptId }
    });

    if (!script) {
      throw new NotFoundException('脚本不存在');
    }

    // 验证脚本
    const validationResult = await this.validateScript(script);
    if (!validationResult.valid) {
      throw new BadRequestException(`脚本验证失败: ${validationResult.errors.join(', ')}`);
    }

    // 创建执行记录
    const execution = this.executionRepository.create({
      scriptId,
      executedBy: context.userId,
      executedByName: 'User',
      status: ExecutionStatus.PENDING,
      inputParameters: context.parameters,
      environment: ExecutionEnvironment.CLIENT,
      startedAt: new Date(),
      logs: [],
      performance: {
        memoryUsage: 0,
        cpuTime: 0,
        nodeExecutions: 0
      }
    });

    const savedExecution = await this.executionRepository.save(execution);

    // 添加到执行队列
    await this.executionQueue.add('execute-script', {
      executionId: savedExecution.id,
      scriptId,
      script: script.graph,
      context
    }, {
      timeout: context.timeout,
      attempts: 1,
      removeOnComplete: false,
      removeOnFail: false
    });

    // 发送执行开始事件
    this.eventEmitter.emit('script.execution.started', {
      executionId: savedExecution.id,
      scriptId,
      userId: context.userId
    });

    return {
      id: savedExecution.id,
      status: ExecutionStatus.PENDING,
      startTime: savedExecution.startTime,
      logs: [],
      performance: {
        memoryUsage: 0,
        cpuTime: 0,
        nodeExecutions: 0
      }
    };
  }

  /**
   * 停止脚本执行
   */
  async stopExecution(executionId: string, userId: string): Promise<void> {
    this.logger.log(`停止执行: ${executionId}`);

    const execution = await this.executionRepository.findOne({
      where: { id: executionId, userId }
    });

    if (!execution) {
      throw new NotFoundException('执行记录不存在');
    }

    if (execution.status !== ExecutionStatus.RUNNING && execution.status !== ExecutionStatus.PENDING) {
      throw new BadRequestException('执行已结束，无法停止');
    }

    // 更新状态
    execution.status = ExecutionStatus.CANCELLED;
    execution.endTime = new Date();
    execution.duration = execution.endTime.getTime() - execution.startTime.getTime();
    await this.executionRepository.save(execution);

    // 从队列中移除
    const job = await this.executionQueue.getJob(executionId);
    if (job) {
      await job.remove();
    }

    // 清理活动执行
    this.activeExecutions.delete(executionId);

    // 发送停止事件
    this.eventEmitter.emit('script.execution.stopped', {
      executionId,
      userId
    });
  }

  /**
   * 获取执行历史
   */
  async getExecutionHistory(
    scriptId: string,
    userId: string,
    options: { page: number; limit: number }
  ) {
    const { page, limit } = options;
    const skip = (page - 1) * limit;

    const [executions, total] = await this.executionRepository.findAndCount({
      where: { scriptId, userId },
      order: { startTime: 'DESC' },
      skip,
      take: limit
    });

    return {
      executions: executions.map(this.formatExecutionResult),
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  /**
   * 获取执行详情
   */
  async getExecutionDetail(executionId: string, userId: string): Promise<ExecutionResult> {
    const execution = await this.executionRepository.findOne({
      where: { id: executionId, userId }
    });

    if (!execution) {
      throw new NotFoundException('执行记录不存在');
    }

    return this.formatExecutionResult(execution);
  }

  /**
   * 验证脚本
   */
  private async validateScript(script: VisualScript): Promise<{ valid: boolean; errors: string[] }> {
    const errors: string[] = [];

    try {
      // 检查脚本结构
      if (!script.graph || !script.graph.nodes) {
        errors.push('脚本图形数据无效');
      }

      // 检查节点连接
      const nodes = script.graph.nodes || [];
      const connections = script.graph.connections || [];

      // 验证节点类型
      for (const node of nodes) {
        if (!node.type) {
          errors.push(`节点 ${node.id} 缺少类型信息`);
        }
      }

      // 验证连接
      for (const connection of connections) {
        const sourceNode = nodes.find(n => n.id === connection.sourceNodeId);
        const targetNode = nodes.find(n => n.id === connection.targetNodeId);

        if (!sourceNode) {
          errors.push(`连接源节点 ${connection.sourceNodeId} 不存在`);
        }

        if (!targetNode) {
          errors.push(`连接目标节点 ${connection.targetNodeId} 不存在`);
        }
      }

      // 检查循环依赖
      if (this.hasCircularDependency(nodes, connections)) {
        errors.push('脚本存在循环依赖');
      }

    } catch (error) {
      errors.push(`验证过程出错: ${error.message}`);
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 检查循环依赖
   */
  private hasCircularDependency(nodes: any[], connections: any[]): boolean {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const dfs = (nodeId: string): boolean => {
      if (recursionStack.has(nodeId)) {
        return true; // 发现循环
      }

      if (visited.has(nodeId)) {
        return false;
      }

      visited.add(nodeId);
      recursionStack.add(nodeId);

      // 查找所有从当前节点出发的连接
      const outgoingConnections = connections.filter(c => c.sourceNodeId === nodeId);
      
      for (const connection of outgoingConnections) {
        if (dfs(connection.targetNodeId)) {
          return true;
        }
      }

      recursionStack.delete(nodeId);
      return false;
    };

    // 检查所有节点
    for (const node of nodes) {
      if (!visited.has(node.id)) {
        if (dfs(node.id)) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * 格式化执行结果
   */
  private formatExecutionResult(execution: any): ExecutionResult {
    return {
      id: execution.id,
      status: execution.status,
      startTime: execution.startTime,
      endTime: execution.endTime,
      duration: execution.duration,
      output: execution.output,
      error: execution.error,
      logs: execution.logs || [],
      performance: execution.performance || {
        memoryUsage: 0,
        cpuTime: 0,
        nodeExecutions: 0
      }
    };
  }

  /**
   * 处理执行队列任务
   */
  async processExecutionJob(job: any): Promise<void> {
    const { executionId, scriptId, script, context } = job.data;

    try {
      this.logger.log(`处理执行任务: ${executionId}`);

      // 更新状态为运行中
      await this.updateExecutionStatus(executionId, ExecutionStatus.RUNNING);

      // 创建执行环境
      const executionEnvironment = await this.createExecutionEnvironment(context);

      // 执行脚本
      const result = await this.runScript(script, executionEnvironment);

      // 更新执行结果
      await this.updateExecutionResult(executionId, {
        status: ExecutionStatus.COMPLETED,
        output: result.output,
        performance: result.performance,
        logs: result.logs
      });

      // 发送完成事件
      this.eventEmitter.emit('script.execution.completed', {
        executionId,
        scriptId,
        result
      });

    } catch (error) {
      this.logger.error(`执行失败: ${executionId}`, error);

      // 更新错误状态
      await this.updateExecutionResult(executionId, {
        status: ExecutionStatus.FAILED,
        error: error.message
      });

      // 发送失败事件
      this.eventEmitter.emit('script.execution.failed', {
        executionId,
        scriptId,
        error: error.message
      });
    } finally {
      // 清理执行环境
      this.activeExecutions.delete(executionId);
    }
  }

  /**
   * 创建执行环境
   */
  private async createExecutionEnvironment(context: ExecutionContext): Promise<any> {
    return {
      userId: context.userId,
      parameters: context.parameters,
      environment: context.environment,
      debug: context.debug,
      startTime: Date.now(),
      logs: [],
      performance: {
        memoryUsage: 0,
        cpuTime: 0,
        nodeExecutions: 0
      }
    };
  }

  /**
   * 运行脚本
   */
  private async runScript(script: any, environment: any): Promise<any> {
    // 这里实现具体的脚本执行逻辑
    // 模拟执行过程
    const startTime = Date.now();
    
    // 模拟节点执行
    const nodes = script.nodes || [];
    const nodeExecutions: NodeExecutionInfo[] = [];

    for (const node of nodes) {
      const nodeStartTime = Date.now();
      
      // 模拟节点执行
      await new Promise(resolve => setTimeout(resolve, Math.random() * 100));
      
      const nodeEndTime = Date.now();
      
      nodeExecutions.push({
        nodeId: node.id,
        nodeType: node.type,
        startTime: nodeStartTime,
        endTime: nodeEndTime,
        duration: nodeEndTime - nodeStartTime,
        status: 'success'
      });

      environment.performance.nodeExecutions++;
    }

    const endTime = Date.now();
    
    return {
      output: {
        result: 'success',
        nodeExecutions
      },
      performance: {
        ...environment.performance,
        cpuTime: endTime - startTime,
        memoryUsage: Math.random() * 100
      },
      logs: environment.logs
    };
  }

  /**
   * 更新执行状态
   */
  private async updateExecutionStatus(executionId: string, status: ExecutionStatus): Promise<void> {
    await this.executionRepository.update(executionId, { status });
  }

  /**
   * 更新执行结果
   */
  private async updateExecutionResult(executionId: string, result: Partial<any>): Promise<void> {
    const updateData: any = { ...result };
    
    if (result.status === ExecutionStatus.COMPLETED || result.status === ExecutionStatus.FAILED) {
      updateData.endTime = new Date();
      
      const execution = await this.executionRepository.findOne({ where: { id: executionId } });
      if (execution) {
        updateData.duration = updateData.endTime.getTime() - execution.startTime.getTime();
      }
    }

    await this.executionRepository.update(executionId, updateData);
  }
}
