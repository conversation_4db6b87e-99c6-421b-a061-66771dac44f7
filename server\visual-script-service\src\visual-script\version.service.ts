import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ScriptVersion } from './entities/script-version.entity';
import { VisualScript } from './entities/visual-script.entity';
import { CreateVersionDto, QueryVersionDto, CompareVersionDto } from './dto/version.dto';

@Injectable()
export class VersionService {
  constructor(
    @InjectRepository(ScriptVersion)
    private versionRepository: Repository<ScriptVersion>,
    @InjectRepository(VisualScript)
    private scriptRepository: Repository<VisualScript>,
    private eventEmitter: EventEmitter2,
  ) {}

  /**
   * 创建脚本版本
   */
  async createVersion(scriptId: string, createVersionDto: CreateVersionDto, userId: string) {
    // 获取脚本
    const script = await this.scriptRepository.findOne({
      where: { id: scriptId },
      relations: ['collaborators'],
    });

    if (!script) {
      throw new NotFoundException('脚本不存在');
    }

    // 检查编辑权限
    if (!this.canEditScript(script, userId)) {
      throw new ForbiddenException('没有编辑权限');
    }

    // 获取当前最新版本号
    const latestVersion = await this.versionRepository.findOne({
      where: { scriptId },
      order: { versionNumber: 'DESC' },
    });

    const nextVersionNumber = latestVersion ? latestVersion.versionNumber + 1 : 1;

    // 创建版本
    const version = this.versionRepository.create({
      scriptId,
      versionNumber: nextVersionNumber,
      name: createVersionDto.name || `v${nextVersionNumber}`,
      description: createVersionDto.description,
      content: createVersionDto.content || script.content,
      metadata: createVersionDto.metadata || {},
      createdBy: userId,
    });

    const savedVersion = await this.versionRepository.save(version);

    // 发送版本创建事件
    this.eventEmitter.emit('script.version.created', {
      scriptId,
      versionId: savedVersion.id,
      userId,
    });

    return savedVersion;
  }

  /**
   * 获取版本列表
   */
  async getVersions(scriptId: string, queryDto: QueryVersionDto, userId: string) {
    // 检查脚本访问权限
    await this.checkScriptAccess(scriptId, userId);

    const { page = 1, limit = 20, sortBy = 'versionNumber', sortOrder = 'DESC' } = queryDto;
    const skip = (page - 1) * limit;

    const queryBuilder = this.versionRepository.createQueryBuilder('version')
      .where('version.scriptId = :scriptId', { scriptId })
      .orderBy(`version.${sortBy}`, sortOrder as 'ASC' | 'DESC')
      .skip(skip)
      .take(limit);

    const [versions, total] = await queryBuilder.getManyAndCount();

    return {
      versions,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  /**
   * 获取版本详情
   */
  async getVersion(scriptId: string, versionId: string, userId: string) {
    // 检查脚本访问权限
    await this.checkScriptAccess(scriptId, userId);

    const version = await this.versionRepository.findOne({
      where: { id: versionId, scriptId },
    });

    if (!version) {
      throw new NotFoundException('版本不存在');
    }

    return version;
  }

  /**
   * 删除版本
   */
  async deleteVersion(scriptId: string, versionId: string, userId: string) {
    const script = await this.scriptRepository.findOne({
      where: { id: scriptId },
      relations: ['collaborators'],
    });

    if (!script) {
      throw new NotFoundException('脚本不存在');
    }

    // 检查编辑权限
    if (!this.canEditScript(script, userId)) {
      throw new ForbiddenException('没有编辑权限');
    }

    const version = await this.versionRepository.findOne({
      where: { id: versionId, scriptId },
    });

    if (!version) {
      throw new NotFoundException('版本不存在');
    }

    // 检查是否为最新版本
    const latestVersion = await this.getLatestVersion(scriptId, userId);
    if (latestVersion.id === versionId) {
      throw new BadRequestException('不能删除最新版本');
    }

    await this.versionRepository.remove(version);

    // 发送版本删除事件
    this.eventEmitter.emit('script.version.deleted', {
      scriptId,
      versionId,
      userId,
    });
  }

  /**
   * 恢复到指定版本
   */
  async restoreVersion(scriptId: string, versionId: string, userId: string) {
    const script = await this.scriptRepository.findOne({
      where: { id: scriptId },
      relations: ['collaborators'],
    });

    if (!script) {
      throw new NotFoundException('脚本不存在');
    }

    // 检查编辑权限
    if (!this.canEditScript(script, userId)) {
      throw new ForbiddenException('没有编辑权限');
    }

    const version = await this.versionRepository.findOne({
      where: { id: versionId, scriptId },
    });

    if (!version) {
      throw new NotFoundException('版本不存在');
    }

    // 更新脚本内容
    script.content = version.content;
    script.updatedAt = new Date();
    script.updatedBy = userId;

    await this.scriptRepository.save(script);

    // 创建新版本记录
    const restoredVersion = await this.createVersion(scriptId, {
      name: `恢复到 ${version.name}`,
      description: `从版本 ${version.name} 恢复`,
      content: version.content,
      metadata: { restoredFrom: versionId },
    }, userId);

    // 发送版本恢复事件
    this.eventEmitter.emit('script.version.restored', {
      scriptId,
      fromVersionId: versionId,
      toVersionId: restoredVersion.id,
      userId,
    });

    return restoredVersion;
  }

  /**
   * 比较版本
   */
  async compareVersions(scriptId: string, compareDto: CompareVersionDto, userId: string) {
    // 检查脚本访问权限
    await this.checkScriptAccess(scriptId, userId);

    const [sourceVersion, targetVersion] = await Promise.all([
      this.versionRepository.findOne({
        where: { id: compareDto.sourceVersionId, scriptId },
      }),
      this.versionRepository.findOne({
        where: { id: compareDto.targetVersionId, scriptId },
      }),
    ]);

    if (!sourceVersion || !targetVersion) {
      throw new NotFoundException('版本不存在');
    }

    // 简单的内容比较
    const diff = this.calculateDiff(sourceVersion.content, targetVersion.content);

    return {
      sourceVersion: {
        id: sourceVersion.id,
        name: sourceVersion.name,
        versionNumber: sourceVersion.versionNumber,
      },
      targetVersion: {
        id: targetVersion.id,
        name: targetVersion.name,
        versionNumber: targetVersion.versionNumber,
      },
      diff,
    };
  }

  /**
   * 获取版本差异
   */
  async getVersionDiff(scriptId: string, versionId: string, targetVersionId: string, userId: string) {
    return this.compareVersions(scriptId, {
      sourceVersionId: versionId,
      targetVersionId,
    }, userId);
  }

  /**
   * 获取最新版本
   */
  async getLatestVersion(scriptId: string, userId: string) {
    // 检查脚本访问权限
    await this.checkScriptAccess(scriptId, userId);

    const version = await this.versionRepository.findOne({
      where: { scriptId },
      order: { versionNumber: 'DESC' },
    });

    if (!version) {
      throw new NotFoundException('没有找到版本');
    }

    return version;
  }

  /**
   * 检查脚本访问权限
   */
  private async checkScriptAccess(scriptId: string, userId: string) {
    const script = await this.scriptRepository.findOne({
      where: { id: scriptId },
      relations: ['collaborators'],
    });

    if (!script) {
      throw new NotFoundException('脚本不存在');
    }

    if (!this.canAccessScript(script, userId)) {
      throw new ForbiddenException('没有访问权限');
    }

    return script;
  }

  /**
   * 检查脚本访问权限
   */
  private canAccessScript(script: VisualScript, userId: string): boolean {
    if (script.ownerId === userId) {
      return true;
    }

    if (script.visibility === 'public') {
      return true;
    }

    // 检查协作者权限
    const collaborator = script.collaborators?.find(c => c.userId === userId);
    return !!collaborator;
  }

  /**
   * 检查脚本编辑权限
   */
  private canEditScript(script: VisualScript, userId: string): boolean {
    if (script.ownerId === userId) {
      return true;
    }

    // 检查协作者权限
    const collaborator = script.collaborators?.find(c => c.userId === userId);
    return collaborator && ['owner', 'editor'].includes(collaborator.role);
  }

  /**
   * 计算内容差异
   */
  private calculateDiff(sourceContent: any, targetContent: any) {
    // 简化的差异计算
    const sourceStr = JSON.stringify(sourceContent, null, 2);
    const targetStr = JSON.stringify(targetContent, null, 2);

    if (sourceStr === targetStr) {
      return { hasChanges: false, changes: [] };
    }

    return {
      hasChanges: true,
      changes: [
        {
          type: 'modified',
          path: 'content',
          oldValue: sourceContent,
          newValue: targetContent,
        },
      ],
    };
  }
}
